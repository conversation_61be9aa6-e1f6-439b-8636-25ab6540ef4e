# SDT 刷新时序问题解决方案文档

## 问题描述

在 SDT (Schema Data Tree) 组件刷新过程中发现了以下关键时序问题：

### 1. 父子节点加载时序混乱
- **现象**: 子级节点（如 `/root/3`）可能在父级节点（如 `/root`）未完成加载时就开始请求
- **原因**: Tree 组件根据 `expandedKeys` 自动触发 `onLoadData`，不按层级顺序
- **后果**: 子级请求被搁置，父级完成后子级不会重试，导致节点无法展开

### 2. 重复请求问题
- **现象**: 刷新时同一个节点会被请求两次
  ```
  第一轮（Tree自动）: /root/3 -> /root  
  第二轮（刷新逻辑）: /root -> /root/3
  ```
- **原因**: 
  1. Tree 组件发现 `expandedKeys` 中的节点需要展开，自动触发加载
  2. `refreshOnRoot` 执行后又重新请求相同节点

### 3. 并发刷新竞态条件
- **现象**: 快速多次点击刷新按钮会导致状态错乱
- **原因**: 多个刷新请求并发执行，状态管理混乱

## 解决方案实施

### 1. 创建父子依赖管理机制

**位置**: `src/pageTabs/queryPage/sdt/sdtSlice.ts:134-170`

```typescript
// 获取节点的直接父级路径
const getDirectParentPath = (nodePath: string): string | null => {
  if (nodePath === '/root') return null;
  const segments = nodePath.split('/');
  if (segments.length <= 2) return null;
  return segments.slice(0, -1).join('/');
}

// 等待父级节点加载完成的 Promise
const waitForParentCompletion = async (parentPath: string, getState: any, maxWaitTime = 10000): Promise<void> => {
  // 使用轮询检查父级加载状态，而不是固定延迟
  // 添加超时保护机制
}
```

**改进点**:
- 替换固定延迟 `timeout * 200ms` 为动态等待机制
- 只等待**直接父级**，而不是所有父级路径
- 添加 10 秒超时保护

### 2. 优化刷新逻辑防止重复请求

**位置**: `src/pageTabs/queryPage/sdt/sdtSlice.ts:501-551`

```typescript
export const refreshOnRoot = (): AppThunk => async(dispatch, getState) => {
  // 防止并发刷新
  if (isRefreshing || isRefreshingNode) return;
  
  // 关键：优先级顺序
  // 1. 首先设置刷新状态，阻止新请求
  dispatch(setIsRefreshingNode(true))
  
  // 2. 立即清空 expandedKeys，防止 Tree 自动触发
  dispatch(setExpandedKeys([]))
  
  // 3. 然后清理其他状态
  // 4. 最后执行实际刷新
}
```

**改进点**:
- 优先设置 `isRefreshingNode=true` 阻止新请求
- 立即清空 `expandedKeys` 防止 Tree 组件自动加载
- 添加双重防护：模块级 `isRefreshing` + Redux 状态 `isRefreshingNode`

### 3. 增强节点加载逻辑

**位置**: `src/pageTabs/queryPage/sdt/sdtSlice.ts:177-242`

```typescript
export const getTreeNodeChildren = createAsyncThunk(
  'sdt/getTreeNodeChildren', 
  async (params, { dispatch, getState }) => {
    // 1. 检查是否正在刷新
    const { sdt: { isRefreshingNode } } = getState();
    if (isRefreshingNode) {
      console.log('正在刷新中，跳过节点加载请求');
      return [];
    }
    
    // 2. 检查直接父级是否需要等待
    const directParentPath = getDirectParentPath(nodePath);
    if (directParentPath && currentFetching[directParentPath]) {
      await waitForParentCompletion(directParentPath, getState);
    }
    
    // 3. 执行实际请求
  }
)
```

**改进点**:
- 添加刷新状态检查，避免刷新过程中响应 Tree 自动加载
- 实现真正的父子依赖等待，确保加载顺序
- 添加详细的调试日志

### 4. 调试日志系统

添加了详细的控制台日志来追踪：
- 节点加载开始/完成
- 父级等待过程
- 缓存使用情况
- 刷新流程各个步骤

## 当前问题状态

### 已解决的问题 ✅
1. **父子依赖管理**: 实现了基于 Promise 的父级等待机制
2. **并发刷新防护**: 添加了双重防护机制
3. **调试能力**: 添加了详细的日志系统
4. **代码质量**: 修复了 TypeScript 编译错误

### 仍存在的问题 ❌
1. **展开状态丢失**: 刷新后 `expandedKeys` 被清空，原有展开状态丢失
2. **重复请求**: 仍然出现相同的四个请求，顺序未变
3. **展开逻辑**: Tree 组件的自动展开行为仍未完全控制

## 下一步优化方向

### 1. 保留展开状态
```typescript
export const refreshOnRoot = (): AppThunk => async(dispatch, getState) => {
  // 保存当前的 expandedKeys
  const { sdt: { expandedKeys: currentExpandedKeys } } = getState();
  
  // 执行刷新逻辑...
  
  // 刷新完成后恢复 expandedKeys（在 fetchConnections 完成后）
}
```

### 2. 更彻底的请求控制
- 在 `onLoadData` 回调中添加刷新状态检查
- 考虑在刷新期间完全禁用 Tree 的 `loadData` 属性

### 3. 分层加载队列
- 实现真正的按层级排序的加载队列
- 确保浅层级节点优先完成加载

## 技术细节

### 关键文件
- `src/pageTabs/queryPage/sdt/sdtSlice.ts` - 主要逻辑
- `src/pageTabs/queryPage/sdt/Sdt.tsx` - UI 组件和 Tree 配置

### 关键状态
- `isRefreshingNode`: Redux 状态，控制整体刷新
- `isRefreshing`: 模块级变量，防止并发
- `onLoadQueue`: 跟踪正在加载的节点队列
- `treeNodeChildrenFetching`: 跟踪每个节点的加载状态

### Tree 组件关键属性
- `expandedKeys`: 控制展开的节点
- `loadedKeys`: 已加载的节点keys  
- `loadData`: 异步加载回调
- `onExpand`: 展开/收起回调

## 总结

本次修复主要解决了父子节点加载的时序问题，建立了基于 Promise 的依赖等待机制。虽然在防止重复请求和保留展开状态方面还需要进一步优化，但核心的父子依赖管理已经实现，为后续的完善奠定了基础。

当前的解决方案显著提高了刷新操作的稳定性，减少了由于时序问题导致的节点展开失败。