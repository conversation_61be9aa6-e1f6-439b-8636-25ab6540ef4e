import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useDispatch, useRequest, useSelector } from 'src/hook';
import { Button, Form, Input, Tabs, Typography, message } from 'antd';
import { useForm } from 'antd/lib/form/Form';
import { DatabaseInfo, setNewSdtSelectedKeysVal, updatePaneInfo, updateTabsInfo } from '../queryTabsSlice'
import { designViewGenerateSql, executeMenuActionSql, getDesignView } from 'src/api';
import { ENABLE_EDIT_VIEWNAME } from './constants';
import { getSchemaFormConnectionType, getObjectNameFormConnectionType } from 'src/util';
import { refreshOnRoot } from '../../sdt';
import styles from './index.module.scss';
import * as _ from 'lodash';
import { useTranslation } from 'react-i18next';

const { TabPane } = Tabs
const { Text } = Typography;
const { TextArea } = Input;

interface DesignNewViewPaneProps {
  databaseInfo: Partial<DatabaseInfo>
  queryTabKey: string
}

export const DesignNewViewPane = (props: DesignNewViewPaneProps) => {
  const { databaseInfo, queryTabKey } = props
  const dispatch = useDispatch()
  const { t } = useTranslation()

  // 缓存初始节点参数
  const currentNodeRef = useRef<Partial<any>>({})

  let {
    connectionId,
    connectionType = 'MySQL',
    databaseName,
    nodePath,
    nodePathWithType,
    nodeType,
  } = databaseInfo

  // 使用更精确的选择器，只订阅需要的数据
  const connection = useSelector((state) =>
    state.editor.toolbarConnections?.find((t) => t.connectionId === connectionId),
    (left, right) => left?.connectionId === right?.connectionId && left?.alias === right?.alias && left?.nodeName === right?.nodeName
  )
  const connectionName = connection?.alias
    ? connection.alias
    : connection?.nodeName

  const [form] = useForm()
  const textAreaAutoSize = { minRows: 11, maxRows: 11 }
  const [activeKey, setActiveKey] = useState<string>("viewText")
  const [sql, setSql] = useState<string>('')
  const [viewName, setViewName] = useState<string>('')
  const [newViewName, setNewViewName] = useState<string>('')

  const nodeName = nodePath? getObjectNameFormConnectionType('view', connectionType, nodePath) : '';

  const getSchemaOrNodeName = (key: string, nodePath: string | undefined) => {
    if (!nodePath) return ;
    let transformedName: string | undefined = '';
    if (key === "schema") {
      transformedName = getSchemaFormConnectionType(connectionType, nodePath)
    } else if (key === "node") {
      transformedName = getObjectNameFormConnectionType('view', connectionType, nodePath)
    }
    return transformedName;
  }

  // 更新时设置信息
  useEffect(() => {
    const refNodeName = currentNodeRef?.current?.nodeName;
    if (refNodeName) {
      setViewName(refNodeName)
      setNewViewName(refNodeName)
    }
  }, [currentNodeRef?.current?.nodeName])

  // 查询视图
  const { data: designViewData, run: getDesignViewRun } = useRequest(
    () => {
      if (!(connectionId && connectionType && nodeType)) return
      return getDesignView({
        connectionId,
        connectionType,
        nodePath: currentNodeRef?.current?.nodePath || nodePath,
        nodePathWithType: currentNodeRef?.current?.nodePathWithType || nodePathWithType,
        nodeName: currentNodeRef?.current?.nodeName || nodeName,
        nodeType,
      })
    },
    {
      onSuccess: (data) => {
        currentNodeRef.current = {
          ...databaseInfo,
          ...currentNodeRef.current,
          ...data,
          nodeName:(() => getSchemaOrNodeName('node', currentNodeRef.current?.nodePath || nodePath))(),
          schemaName: (() => getSchemaOrNodeName('schema', currentNodeRef.current?.nodePath || nodePath))()
        }
      }
    }
  )

  // 生成 sql预览
  const { run: designViewGenerateSqlRun } = useRequest(
    (params) => {
      const { viewText, viewName, comment } = params;
      return designViewGenerateSql({
        connectionType,
        databaseName,
        schemaName: currentNodeRef?.current?.schemaName,
        viewName: currentNodeRef?.current?.nodeName,
        viewText,
        viewInfo: {
          newViewName: viewName,
          oldComment: designViewData?.comment,
          comment,
        }
      })
    },{
      manual: true,
      onSuccess: (data: any) => {
        setSql(data?.sql);
      },
    },
  )

  useEffect(() => {
    setSql(designViewData?.generatedSql || '')
    form.setFieldsValue(designViewData)
  }, [JSON.stringify(designViewData)])

  // 更新状态
  const updateStatus = async() => {
    const { nodeName: viewName, nodePath, nodePathWithType } = currentNodeRef?.current || {};
    if (viewName) {
      const newNodeName = newViewName;
      const newNodePath = nodePath?.replace(viewName, newViewName);
      const newNodePathWithType = nodePathWithType?.replace(viewName, newViewName);
      dispatch(setNewSdtSelectedKeysVal(newNodePath || nodePath || ''));

      currentNodeRef.current = {
        ...currentNodeRef.current,
        nodeName: newNodeName,
        nodePath: newNodePath,
        nodePathWithType: newNodePathWithType,
      }
    } else {
      dispatch(setNewSdtSelectedKeysVal(''));
    }
    getDesignViewRun();
  }

  //修改表名 刷新 store内容没有更新问题 手动更新
  const onUpdateTabInfo = () => {
    const newFileds = {
      key: queryTabKey,
      nodeName: newViewName,
      nodePath:
        nodePath && viewName && _.replace(nodePath, viewName, newViewName),
    }
    dispatch(updateTabsInfo({ key: queryTabKey, tabName: newViewName }))
    dispatch(updatePaneInfo({ key: queryTabKey, paneInfo: newFileds }))
  }

  // 执行修改sql
  const onClickExecSql = async () => {
    if (connectionId) {
      const result = await executeMenuActionSql({
        connectionId: connectionId,
        dataSourceType: connectionType,
        databaseName,
        operatingObject: currentNodeRef?.current?.schemaName,
        tabKey: queryTabKey,
        statements: [sql],
      })

      const executeResultFlag = result.reduce((pre, cur) => {
        if (cur.success) pre++
        return pre
      }, 0)
      //change viewName should update tabInfo
      if (result?.[0]?.success && newViewName !== viewName) {
        onUpdateTabInfo()
      }
      if (executeResultFlag === result.length) {
        message.success(t('sdo_execution_suc'))
      } else if (executeResultFlag > 0) {
        message.warning(t('sdo_partial_statement_execution_fail'))
      } else {
        message.error(t('sdo_execution_fail'))
      }

      await updateStatus()

      dispatch(refreshOnRoot())
    }
  }

  const onFinish = () => {
    form.validateFields(['viewName'])
      .then(async () => {
        if (sql) {
          await onClickExecSql()
        }
      })
      .catch((error) => {
        console.log('提交失败 :>> ', error);
      })
  };

  // 保存信息
  const onSaveInfo = async () => {
    form.validateFields(['viewName']).then((val: any) => {
      const values = form.getFieldsValue();
      designViewGenerateSqlRun(values)
    })
  }

  // 切换基本信息 表名，表注释
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const onchangeBaseInfo = useCallback(_.throttle(onSaveInfo, 1000), [])
  
  return (
    <div className={styles.designViewPane}>
      <Form
        form={form}
        labelCol={{ span: 2 }}
        wrapperCol={{ span: 22 }}
        onFinish={onFinish}
      >
        <Form.Item label={t('sdo_connection_name')}>
          <Text>{connectionName}</Text>
        </Form.Item>
        <Form.Item label={t('sdo_database')}>
          <Text>{databaseName}</Text>
        </Form.Item>
        <Form.Item label={t('sdo_view_name')}
          name="viewName"
          rules={[{ required: true }]}
          initialValue={currentNodeRef?.current?.nodeName || nodeName}
        >
          {
            ENABLE_EDIT_VIEWNAME.includes(connectionType?.toLocaleLowerCase() || '') ?
              <Input
                style={{ width: 360 }}
                onChange={(e) => {
                  setNewViewName(e.target.value)
                  onchangeBaseInfo()
                }}
              />
              : <Text>&nbsp;{currentNodeRef?.current?.nodeName || nodeName || '-'}</Text>
          }
        </Form.Item>
        <Form.Item label={t('sdo_comment')}
          name="comment"
        >
          <TextArea
            style={{ width: 360 }}
            onChange={() => onchangeBaseInfo()}
          />
        </Form.Item>
        <div style={{ height: "auto", marginBottom: "3px" }}>
          <Tabs
            tabPosition="top"
            className={styles.tab}
            activeKey={activeKey}
            onChange={async (active) => {
              setActiveKey(active)
            }}
          >
            <TabPane tab={t('sdo_definition')} key="viewText" tabKey="viewText">
              <Form.Item
                label=""
                name="viewText"
                colon={false}
              >
                <TextArea autoSize={textAreaAutoSize} onChange={() => onchangeBaseInfo()} />
              </Form.Item>
            </TabPane>
            <TabPane tab={t('sdo_sql_preview')} key="sql" tabKey="sql">
              <Form.Item
                label=""
                colon={false}
              >
                <TextArea
                  placeholder={t('sdo_edit_view_sql_preview')}
                  autoSize={textAreaAutoSize}
                  value={sql}
                  readOnly
                />
              </Form.Item>
            </TabPane>
          </Tabs>
        </div>
        {
          activeKey === "sql" &&
          <Form.Item>
            <Button type="primary" htmlType="submit">
              {t('sdo_submit')}
            </Button>
          </Form.Item>
        }
      </Form>
    </div>
  )
}