const MonacoWebpackPlugin = require('monaco-editor-webpack-plugin')
const SpeedMeasurePlugin = require('speed-measure-webpack-plugin')
const BundleAnalyzerPlugin =
  require('webpack-bundle-analyzer').BundleAnalyzerPlugin
const CracoAntDesignPlugin = require('craco-antd')
const { removeLoaders, loaderByName }  = require('@craco/craco')
const path = require('path')
const TerserPlugin = require("terser-webpack-plugin")
const smp = new SpeedMeasurePlugin()

// const TARGET_HOST = `http://*************:64480/`// 安徽政务云
// const TARGET_HOST = `http://************:3000`
const TARGET_HOST = `http://************`
// const TARGET_HOST = `http://***********:80` //彭志亨
// const TARGET_HOST = `http://*************` // 慕雨伦
// const TARGET_HOST = `http://*************:80` // 徐鸿亮
// const TARGET_HOST = `http://************:9899`
// const TARGET_HOST = `http://*************:9899`  // 梦洋
// const TARGET_HOST = `http://************:9899` // 张汇金
// const TARGET_HOST = `http://*************`
// const TARGET_HOST = `http://************:9899` // 许阳
// const TARGET_HOST = `http://*************:9899`
// const TARGET_HOST = `http://*************:9899` //世铭
// const TARGET_HOST = `http://*************` // 耀磊
// const TARGET_HOST = `http://*************:9899`
// const TARGET_HOST = `http://************:9899` // 唐波
// const TARGET_HOST = `http://************:9898` //张学蒙
// const TARGET_HOST = `http://*************` 
// const TARGET_HOST = `http://*************` //姚仲凯base-2.3.6服务
// const TARGET_HOST = `http://*************`  //测试服务
// const TARGET_HOST = `http://*************` //邢利民本地服务

module.exports = {
  webpack: smp.wrap({
    plugins: [
      new MonacoWebpackPlugin({
        languages: ['mysql', 'sql', 'pgsql', 'redis'],
        features: ['coreCommands'],
      }),
      new BundleAnalyzerPlugin({
        analyzerMode: process.env.REACT_APP_ANALYZING ? 'server' : 'disabled',
      }),
    ],
    configure: (webpackConfig, { env }) => {
      webpackConfig.optimization.splitChunks = {
        chunks: 'all',
        name: false,
        cacheGroups: {
          monaco: {
            test: /[\\/]node_modules[\\/]monaco-editor[\\/]/,
            priority: -1,
          },
          defaultVendors: {
            test: /[\\/]node_modules[\\/]/,
            priority: -10,
          },
          default: {
            minChunks: 2,
            priority: -20,
            reuseExistingChunk: true,
          },
        },
      }
      if (env !== 'development') {
        webpackConfig.optimization.minimizer = [
          new TerserPlugin({
            parallel: true, // 开启并行压缩
          })
        ]
        // 生产环境关闭sourcemap
        webpackConfig.devtool = false
        removeLoaders(webpackConfig, loaderByName('source-map-loader'))
      }
      
      webpackConfig.externals = { editor_parser: 'editor_parser'}
      webpackConfig.resolve.alias = {
        vscode: require.resolve(
          'monaco-languageclient/lib/vscode-compatibility',
        ),
        ...webpackConfig.resolve.alias,
      }
      return webpackConfig
    },
  }),
  plugins: [
    {
      plugin: CracoAntDesignPlugin,
      options: {
        customizeThemeLessPath: path.join(__dirname, 'src/styles/theme.less'),
      },
    },
  ],
  devServer: {
    proxy: [
      {
        context: [
          '/dms',
          '/user',
          '/audit',
          '/taskCenter',
          '/message',
          '/monitor',
          '/desens',
          '/api/flow',
          '/export',
          '/gateway',
          '/dbggw',
          '/analyze',
          '/clientManager',
          '/license',
          '/setFile'
        ],
        target: TARGET_HOST,
      },
      {
        context: ['/socket.io', '/terminal', '/cqupgrade', '/lspserver'],
        target: TARGET_HOST,
        ws: true,
        // https://github.com/webpack/webpack-dev-server/issues/1642#issuecomment-733917174
        // https://github.com/maknapp/webpack-dev-server/commit/3b1e9c7765d432480788a7a68019f9a29b997099
        onProxyReqWs: (_proxyReq, _req, socket) => {
          socket.on('error', (err) => {
            console.warn('WebSocket error', err.message)
          })
        },
      },
    ],
  },
  eslint: {
    enable: false,
  },
}
